import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class EmailLogs extends BaseSchema {
  protected tableName = 'email_logs'

  public async up () {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })

      // Email campaign and recipient info
      table.string('campaign_id').nullable()
      table.string('recipient_email').notNullable()
      table.text('recipient_note').nullable()

      // Email content
      table.string('subject', 500).notNullable()
      table.text('message_preview').nullable()

      // Status and error tracking
      table.string('status', 50).defaultTo('pending') // pending, sent, failed, retry
      table.text('error_message').nullable()
      table.integer('attempts').defaultTo(0)
      table.timestamp('last_attempt_at').nullable()
      table.timestamp('sent_at').nullable()

      // Batch and checkpoint info
      table.integer('batch_number').nullable().defaultTo(0)
      table.integer('checkpoint_index').nullable().defaultTo(0)

      // User info
      table.integer('user_id').nullable()
      table.string('user_name').nullable()

      // Indexes for performance
      table.index(['campaign_id'])
      table.index(['status'])
      table.index(['recipient_email'])
      table.index(['created_at'])
    })
  }

  public async down () {
    this.schema.dropTable(this.tableName)
  }
}
