// Пример использования оптимизированного AI чата
// Этот файл демонстрирует различные стратегии использования нового API

// ============================================================================
// СТРАТЕГИЯ 1: БЫСТРЫЙ ОТВЕТ (РЕКОМЕНДУЕТСЯ)
// ============================================================================

async function quickChatExample() {
  console.log('🚀 Пример быстрого чата')
  
  // 1. Создаем новый тред
  const { threadId } = await trpc.services.generateChatThread.query()
  console.log('📝 Создан новый тред:', threadId)
  
  // 2. Отправляем сообщение с оптимизированным методом
  const startTime = Date.now()
  
  const response = await trpc.services.aiChatOptimized.mutate({
    message: "Ищу уплотнители для двигателя ВАЗ 2110",
    threadId: threadId,
    enableProductSearch: true
  })
  
  const responseTime = Date.now() - startTime
  console.log(`⚡ Получен ответ за ${responseTime}мс`)
  console.log('💬 Ответ:', response.response)
  
  // 3. Показываем ответ пользователю сразу
  displayMessage({
    text: response.response,
    threadId: response.threadId,
    timestamp: new Date()
  })
  
  // 4. Если поиск товаров в процессе, показываем индикатор
  if (response.searchInProgress) {
    console.log('🔍 Поиск товаров выполняется в фоне...')
    showSearchIndicator()
    
    // В реальном приложении здесь можно:
    // - Показать спиннер "Ищем товары..."
    // - Использовать WebSocket для получения результатов
    // - Периодически проверять статус поиска
  }
  
  return response
}

// ============================================================================
// СТРАТЕГИЯ 2: С ОБОГАЩЕНИЕМ ОТВЕТА
// ============================================================================

async function enrichedChatExample() {
  console.log('🚀 Пример чата с обогащением')
  
  // 1. Получаем быстрый ответ
  const quickResponse = await trpc.services.aiChatOptimized.mutate({
    message: "Нужны сальники коленвала для Лада Калина",
    threadId: "existing-thread-id",
    enableProductSearch: true
  })
  
  console.log('💬 Быстрый ответ:', quickResponse.response)
  displayMessage({ text: quickResponse.response })
  
  // 2. Симулируем получение товаров (в реальности они приходят асинхронно)
  // В реальном приложении товары будут получены через performAsyncProductSearch
  const mockProducts = [
    {
      prod_id: 1,
      prod_purpose: "Сальник коленвала передний",
      prod_price: 450,
      prod_sku: "SK-001",
      prod_manuf: "CORTECO",
      prod_type: "Сальник",
      qty: 5
    },
    {
      prod_id: 2, 
      prod_purpose: "Сальник коленвала задний",
      prod_price: 520,
      prod_sku: "SK-002", 
      prod_manuf: "ELRING",
      prod_type: "Сальник",
      qty: 3
    }
  ]
  
  // 3. Обогащаем ответ информацией о товарах
  if (mockProducts.length > 0) {
    console.log('🔍 Найдено товаров:', mockProducts.length)
    
    const enrichedResponse = await trpc.services.enrichResponseWithProducts.mutate({
      originalResponse: quickResponse.response,
      products: mockProducts,
      threadId: quickResponse.threadId,
      resourceId: quickResponse.resourceId,
      userMessage: "Нужны сальники коленвала для Лада Калина",
      temperature: 0.3
    })
    
    if (enrichedResponse.success) {
      console.log('✅ Ответ обогащен!')
      console.log('💬 Обогащенный ответ:', enrichedResponse.response)
      
      // Обновляем сообщение с обогащенным ответом
      updateMessage({
        text: enrichedResponse.response,
        products: mockProducts,
        enriched: true
      })
    }
  }
}

// ============================================================================
// СТРАТЕГИЯ 3: СРАВНЕНИЕ ПРОИЗВОДИТЕЛЬНОСТИ
// ============================================================================

async function performanceComparison() {
  console.log('🚀 Сравнение производительности')
  
  const testMessage = "Ищу прокладки головки блока цилиндров"
  const threadId = await trpc.services.generateChatThread.query().then(r => r.threadId)
  
  // Тест старого метода
  console.log('📊 Тестируем старый метод...')
  const oldStart = Date.now()
  
  const oldResponse = await trpc.services.aiChat.mutate({
    message: testMessage,
    threadId: threadId + '_old'
  })
  
  const oldTime = Date.now() - oldStart
  console.log(`⏱️ Старый метод: ${oldTime}мс`)
  
  // Тест нового метода
  console.log('📊 Тестируем новый метод...')
  const newStart = Date.now()
  
  const newResponse = await trpc.services.aiChatOptimized.mutate({
    message: testMessage,
    threadId: threadId + '_new',
    enableProductSearch: true
  })
  
  const newTime = Date.now() - newStart
  console.log(`⚡ Новый метод: ${newTime}мс`)
  
  // Результаты
  const improvement = ((oldTime - newTime) / oldTime * 100).toFixed(1)
  console.log(`🎯 Улучшение производительности: ${improvement}%`)
  
  return {
    oldTime,
    newTime,
    improvement: `${improvement}%`,
    oldResponse,
    newResponse
  }
}

// ============================================================================
// СТРАТЕГИЯ 4: ОБРАБОТКА ОШИБОК
// ============================================================================

async function errorHandlingExample() {
  console.log('🚀 Пример обработки ошибок')
  
  try {
    const response = await trpc.services.aiChatOptimized.mutate({
      message: "Тестовое сообщение",
      threadId: "test-thread",
      enableProductSearch: true,
      temperature: 0.5,
      maxRetries: 1, // Уменьшаем для быстрого тестирования
      maxSteps: 2
    })
    
    if (response.success) {
      console.log('✅ Успешный ответ:', response.response)
      
      // Обрабатываем случай, когда поиск товаров в процессе
      if (response.searchInProgress) {
        console.log('🔍 Поиск товаров в процессе...')
        // Здесь можно добавить логику ожидания результатов
      }
    } else {
      console.error('❌ Ошибка в ответе:', response.error)
      handleChatError(response.error)
    }
    
  } catch (error) {
    console.error('❌ Исключение при вызове API:', error)
    handleChatError('NETWORK_ERROR')
  }
}

// ============================================================================
// ВСПОМОГАТЕЛЬНЫЕ ФУНКЦИИ
// ============================================================================

function displayMessage(message) {
  console.log('📱 Отображаем сообщение пользователю:', message.text)
  // В реальном приложении здесь будет обновление UI
}

function updateMessage(message) {
  console.log('🔄 Обновляем сообщение:', message.text)
  // В реальном приложении здесь будет обновление существующего сообщения
}

function showSearchIndicator() {
  console.log('⏳ Показываем индикатор поиска товаров')
  // В реальном приложении здесь будет показ спиннера или прогресс-бара
}

function handleChatError(error) {
  console.log('🚨 Обрабатываем ошибку чата:', error)
  
  const errorMessages = {
    'CONNECTION_REFUSED': 'Сервис временно недоступен. Попробуйте позже.',
    'AGENT_NOT_FOUND': 'AI агент не найден. Обратитесь к администратору.',
    'RATE_LIMIT_EXCEEDED': 'Превышен лимит запросов. Подождите немного.',
    'NETWORK_ERROR': 'Проблемы с сетью. Проверьте подключение к интернету.',
    'UNKNOWN_ERROR': 'Произошла неизвестная ошибка. Попробуйте еще раз.'
  }
  
  const userMessage = errorMessages[error] || errorMessages['UNKNOWN_ERROR']
  displayMessage({ text: userMessage, isError: true })
}

// ============================================================================
// ЗАПУСК ПРИМЕРОВ
// ============================================================================

async function runExamples() {
  console.log('🎬 Запускаем примеры использования оптимизированного AI чата\n')
  
  try {
    // Пример 1: Быстрый ответ
    await quickChatExample()
    console.log('\n' + '='.repeat(50) + '\n')
    
    // Пример 2: С обогащением
    await enrichedChatExample()
    console.log('\n' + '='.repeat(50) + '\n')
    
    // Пример 3: Сравнение производительности
    await performanceComparison()
    console.log('\n' + '='.repeat(50) + '\n')
    
    // Пример 4: Обработка ошибок
    await errorHandlingExample()
    
  } catch (error) {
    console.error('❌ Ошибка при выполнении примеров:', error)
  }
}

// Экспортируем функции для использования в других модулях
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    quickChatExample,
    enrichedChatExample,
    performanceComparison,
    errorHandlingExample,
    runExamples
  }
}

// Если файл запущен напрямую, выполняем примеры
if (typeof window === 'undefined' && require.main === module) {
  runExamples()
}
