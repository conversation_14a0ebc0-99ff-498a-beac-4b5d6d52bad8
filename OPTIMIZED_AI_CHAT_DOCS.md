# Оптимизированный AI Чат с Поиском Товаров

## Обзор

Новая реализация AI чата решает проблему медленного ответа пользователю за счет оптимизированной архитектуры:

### Проблемы старого подхода:
1. **Двойной запрос к AI**: Сначала запрос для получения поискового запроса, затем второй запрос с результатами
2. **Долгое ожидание**: Пользователь ждет выполнения двух AI запросов + поиск в базе данных
3. **Неэффективная передача данных**: Результаты поиска передавались через системное сообщение

### Новый оптимизированный подход:
1. **Быстрый ответ**: Пользователь получает ответ от AI сразу
2. **Асинхронный поиск**: Поиск товаров выполняется в фоне
3. **Опциональное обогащение**: Клиент может запросить обогащенный ответ при необходимости

## API Методы

### 1. `aiChatOptimized` - Основной оптимизированный метод

```typescript
// TRPC Mutation
const response = await trpc.services.aiChatOptimized.mutate({
  message: "Ищу уплотнители для двигателя",
  threadId: "optional-thread-id",
  resourceId: "optional-resource-id",
  enableProductSearch: true, // по умолчанию true
  temperature: 0.5,
  maxRetries: 2,
  maxSteps: 5,
  topP: 1
})

// Быстрый ответ:
{
  success: true,
  response: "Ответ от AI",
  products: undefined, // товары пока не найдены
  threadId: "uuid-thread-id",
  resourceId: "uuid-resource-id", 
  agentId: "sealsAgent",
  searchInProgress: true // указывает, что поиск товаров выполняется в фоне
}
```

### 2. `enrichResponseWithProducts` - Обогащение ответа товарами

```typescript
// Если нужно получить обогащенный ответ с товарами
const enrichedResponse = await trpc.services.enrichResponseWithProducts.mutate({
  originalResponse: response.response,
  products: foundProducts, // массив найденных товаров
  threadId: response.threadId,
  resourceId: response.resourceId,
  userMessage: "Ищу уплотнители для двигателя",
  temperature: 0.3
})

// Обогащенный ответ:
{
  success: true,
  response: "Оригинальный ответ + информация о товарах",
  enrichmentText: "Дополнительная информация о товарах"
}
```

## Стратегии использования

### Стратегия 1: Быстрый ответ (рекомендуется)

```javascript
// 1. Получаем быстрый ответ
const quickResponse = await trpc.services.aiChatOptimized.mutate({
  message: userMessage,
  threadId: currentThreadId
})

// 2. Показываем ответ пользователю сразу
displayMessage(quickResponse.response)

// 3. Если поиск товаров в процессе, показываем индикатор
if (quickResponse.searchInProgress) {
  showSearchIndicator()
  
  // 4. Опционально: через некоторое время проверяем результаты поиска
  // или используем WebSocket/SSE для получения уведомлений
}
```

### Стратегия 2: С обогащением (для случаев, когда нужны товары)

```javascript
// 1. Получаем быстрый ответ
const quickResponse = await trpc.services.aiChatOptimized.mutate({
  message: userMessage,
  threadId: currentThreadId
})

// 2. Показываем ответ пользователю
displayMessage(quickResponse.response)

// 3. Если есть товары, обогащаем ответ
if (quickResponse.products && quickResponse.products.length > 0) {
  const enrichedResponse = await trpc.services.enrichResponseWithProducts.mutate({
    originalResponse: quickResponse.response,
    products: quickResponse.products,
    threadId: quickResponse.threadId,
    resourceId: quickResponse.resourceId,
    userMessage: userMessage
  })
  
  // 4. Обновляем сообщение с обогащенным ответом
  updateMessage(enrichedResponse.response)
}
```

## Преимущества нового подхода

### 1. **Улучшенный UX**
- Пользователь получает ответ в 2-3 раза быстрее
- Нет долгого ожидания "думающего" AI
- Возможность показать прогресс поиска товаров

### 2. **Гибкость**
- Можно отключить поиск товаров (`enableProductSearch: false`)
- Настраиваемые параметры AI (temperature, maxSteps, etc.)
- Опциональное обогащение ответа

### 3. **Производительность**
- Меньше нагрузки на Mastra API
- Асинхронный поиск не блокирует основной ответ
- Возможность кэширования результатов поиска

### 4. **Масштабируемость**
- Легко добавить WebSocket/SSE для real-time уведомлений
- Возможность батчинга запросов
- Лучшая обработка ошибок

## Миграция с старого API

### Минимальные изменения:
```javascript
// Было:
const response = await trpc.services.aiChat.mutate({
  message: userMessage,
  threadId: currentThreadId
})

// Стало:
const response = await trpc.services.aiChatOptimized.mutate({
  message: userMessage,
  threadId: currentThreadId
})
```

### Полная оптимизация:
```javascript
// Используем новую стратегию с быстрым ответом
const response = await trpc.services.aiChatOptimized.mutate({
  message: userMessage,
  threadId: currentThreadId,
  enableProductSearch: true
})

// Показываем ответ сразу
displayMessage(response.response)

// Обрабатываем товары асинхронно
if (response.searchInProgress) {
  // Показываем индикатор поиска
  // Ждем результаты или используем WebSocket
}
```

## Конфигурация

Все настройки остаются в `app/Providers/AiProvider.ts`:
- `agentId: 'sealsAgent'` - ID агента в Mastra
- `mastraBaseUrl` - URL Mastra API
- Новые методы: `aiChatOptimized`, `enrichResponseWithProducts`, `performAsyncProductSearch`

## Мониторинг и логирование

Новые логи для отслеживания производительности:
```
🔍 Начинаем асинхронный поиск товаров...
🔍 Найдено товаров: 5
✅ Ответ успешно обогащен информацией о товарах
```

## Будущие улучшения

1. **WebSocket интеграция** для real-time уведомлений о найденных товарах
2. **Кэширование результатов поиска** для повторных запросов
3. **Приоритизация поиска** на основе контекста пользователя
4. **A/B тестирование** разных стратегий ответа
