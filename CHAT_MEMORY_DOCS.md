# Документация по работе с памятью чата

## Обзор

Система чата теперь поддерживает сохранение истории сообщений с использованием встроенной Memory API от Mastra. Это позволяет AI помнить контекст разговора между сессиями.

## Основные концепции

- **threadId** - уникальный идентификатор треда (беседы)
- **resourceId** - идентификатор ресурса для группировки тредов (по умолчанию равен threadId)
- **Memory API** - встроенная система Mastra для хранения истории

## API методы

### 1. Генерация нового threadId

```typescript
// TRPC Query
const { threadId } = await trpc.services.generateChatThread.query()
```

### 2. Отправка сообщения в чат

```typescript
// TRPC Mutation
const response = await trpc.services.aiChat.mutate({
  message: "Привет! Меня зовут Алексей",
  threadId: "optional-thread-id", // Если не указан, создается новый
  resourceId: "optional-resource-id", // Для группировки чатов
  conversationHistory: [] // Опционально, если хотите передать историю вручную
})

// Ответ:
{
  success: true,
  response: "Ответ от AI",
  threadId: "uuid-thread-id",
  resourceId: "uuid-resource-id",
  agentId: "sealsAgent",
  products: [] // Если AI нашел товары
}
```

### 3. Получение истории чата

```typescript
// TRPC Query
const history = await trpc.services.getChatHistory.query({
  threadId: "thread-id"
})

// Ответ:
{
  success: true,
  threadId: "thread-id",
  messages: {
    uiMessages: [
      {
        id: "msg-id",
        role: "user",
        content: "Сообщение пользователя",
        createdAt: "2025-06-03T21:51:48.415Z"
      },
      {
        id: "msg-id",
        role: "assistant", 
        content: "Ответ AI",
        createdAt: "2025-06-03T21:51:48.416Z"
      }
    ]
  }
}
```

### 4. Удаление истории чата

```typescript
// TRPC Mutation
const result = await trpc.services.deleteChatHistory.mutate({
  threadId: "thread-id"
})
```

### 5. Получение списка тредов

```typescript
// TRPC Query
const threads = await trpc.services.getChatThreadsList.query({
  resourceId: "resource-id"
})
```

## Пример использования

```javascript
// 1. Создаем новый чат
const { threadId } = await trpc.services.generateChatThread.query()

// 2. Отправляем первое сообщение
const firstResponse = await trpc.services.aiChat.mutate({
  message: "Привет! Меня зовут Алексей",
  threadId
})

// 3. Отправляем второе сообщение (AI помнит контекст)
const secondResponse = await trpc.services.aiChat.mutate({
  message: "Как меня зовут?",
  threadId
})
// AI ответит: "Привет, Алексей! Ваше имя запомнил..."

// 4. Получаем полную историю
const history = await trpc.services.getChatHistory.query({ threadId })
```

## Особенности реализации

1. **Автоматическое сохранение** - история сохраняется автоматически в Mastra Memory API
2. **Умная обработка истории** - система автоматически загружает историю при указании threadId
3. **Fallback механизм** - если история не загружается, чат продолжается как новый
4. **Поддержка сложного контента** - обрабатывает как простой текст, так и сложные структуры от AI

## Конфигурация

В `app/Providers/AiProvider.ts`:
- `agentId: 'sealsAgent'` - ID агента в Mastra
- `mastraBaseUrl` - URL Mastra API (по умолчанию http://localhost:4111/api)

## Тестирование

Запустите тест: `node test_chat.js`

Тест проверяет:
- Генерацию threadId
- Отправку сообщений
- Сохранение и загрузку истории
- Работу памяти AI
