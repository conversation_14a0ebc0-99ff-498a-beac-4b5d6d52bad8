import imaps from 'imap-simple'
import Imap from 'imap'
import { simple<PERSON><PERSON><PERSON>, MailParser, ParsedMail } from 'mailparser'

//TODO: move to env | db
const configs: { yandex: Imap.Config; timeweb: Imap.Config } = {
  yandex: {
    user: '<EMAIL>',
    password: 'kniesbdbtjawsrls',
    host: 'imap.yandex.ru',
    port: 993,
    tls: true,
    authTimeout: 3000
  },
  timeweb: {
    user: '<EMAIL>',
    password: 'JYxe8.sh*RfBdl',
    host: 'imap.timeweb.ru',
    port: 993,
    tls: true,
    authTimeout: 3000
  }
}

interface GetEmails {
  searchCriteria: any[]
  box?: 'INBOX' | 'Sent' | 'Draft' | 'Deleted'
  markAsRead?: boolean
  configName?: keyof typeof configs
}

export const getEmails = ({ searchCriteria, box = 'INBOX', markAsRead = false, configName = 'yandex' }: GetEmails): Promise<ParsedMail[]> => {
  return new Promise((resolve, reject) => {
    try {
      const imap = new Imap(configs[configName])

      imap.once('ready', () => {
        imap.openBox(box, false, () => {
          imap.search(searchCriteria, (err, results) => {
            let f

            try {
              f = imap.fetch(results, { bodies: '' })
            } catch (error) {
              // console.log('🚀imap.search ~ error:', error)
              imap.end()
              return reject('empty box')
            }

            const parsedEmails: ParsedMail[] = []

            f.on('message', (msg) => {
              msg.on('body', (stream) => {
                simpleParser(stream, async (err, parsed) => {
                  parsedEmails.push(parsed)
                })
              })

              if (markAsRead) {
                msg.once('attributes', (attrs) => {
                  const { uid } = attrs
                  imap.addFlags(uid, ['\\Seen'], () => {
                    // console.log('Marked as read!')
                  })
                })
              }
            })

            f.once('error', (ex) => {
              reject(ex)
            })

            f.once('end', () => {
              // console.log('Done fetching all messages!')
              imap.end()
              resolve(parsedEmails)
            })
          })
        })
      })

      imap.once('error', (err) => {
        console.log(err)
        reject(err)
      })

      imap.once('end', () => {
        console.log('Connection ended')
      })

      imap.connect()
    } catch (ex) {
      console.log('An error occurred')
      reject(ex)
    }
  })
}

// await getEmails({
//   searchCriteria: [
//     // ['FROM', '<EMAIL>']c
//     ['TO', '<EMAIL>']
//   ],
//   box: 'Sent' //'INBOX'
// })

// const s = async () => {
//   const res = await getEmails({
//     searchCriteria: [
//       // ['OR', ['SEEN'], ['DELETED']],
//       // ['TO', '<EMAIL>'],
//       ['SINCE', '10-Aug-2020']
//     ],
//     configName: 'timeweb',
//     box: 'Sent' //'Sent' //'INBOX',
//   })

//   console.log('res:', res)
// }

// s()
