import axios from 'axios'
import Env from '@ioc:Adonis/Core/Env'
import { v4 as uuidv4 } from 'uuid'
import { productProvider } from './ProductProvider'

interface MastraResponse {
  text?: string
  error?: string
  message?: string
  content?: string
}

interface ChatMessage {
  role: 'user' | 'assistant' | 'system'
  content: string
}

interface MastraRequestBody {
  messages: ChatMessage[]
  runId: string
  maxRetries: number
  maxSteps: number
  temperature: number
  topP: number
  runtimeContext: Record<string, any>
  threadId: string
  resourceId: string
}

interface QueryItem {
  q: string
  filters?: string
}

interface ParsedQuery {
  queries: QueryItem[]
}

function parseQueryContent(input: string): ParsedQuery | null {
  // Регулярное выражение для поиска содержимого между тегами <query> и </query>
  const queryRegex = /<query>\s*([\s\S]*?)\s*<\/query>/i
  const match = input.match(queryRegex)

  if (!match || !match[1]) {
    return null
  }

  try {
    // Парсим JSON содержимое
    const jsonContent = match[1].trim()
    const parsedData = JSON.parse(jsonContent)

    // Проверяем, что это массив
    if (!Array.isArray(parsedData)) {
      throw new Error('Query content must be an array')
    }

    // Валидируем каждый элемент массива
    const queries: QueryItem[] = parsedData.map((item, index) => {
      if (typeof item !== 'object' || item === null) {
        throw new Error(`Item at index ${index} must be an object`)
      }

      if (typeof item.q !== 'string') {
        throw new Error(`Item at index ${index} must have a 'q' property of type string`)
      }

      const queryItem: QueryItem = {
        q: item.q
      }

      // Добавляем filters если он существует и является строкой
      if (item.filters !== undefined) {
        if (typeof item.filters !== 'string') {
          throw new Error(`Item at index ${index} has 'filters' property that is not a string`)
        }
        queryItem.filters = item.filters
      }

      return queryItem
    })

    return {
      queries
    }
  } catch (error) {
    console.error('Ошибка при парсинге содержимого query:', error)
    return null
  }
}

class AiProvider {
  private mastraBaseUrl: string
  private agentId: string

  constructor() {
    this.mastraBaseUrl = Env.get('MASTRA_API_URL', 'http://localhost:4111/api')
    this.agentId = 'sealsAgent'
  }

  /**
   * Генерирует новый threadId для начала новой беседы
   */
  generateThreadId(): string {
    return uuidv4()
  }

  formatProductsForContext(products: any[]): string {
    return products
      .map((product, index) => {
        return `${index + 1}. ${product.prod_purpose || 'Без названия'}
      - Цена: ${product.prod_price || 'Не указана'} руб.
      - Артикулы: ${product.prod_sku}, ${product.prod_analogsku}, ${product.prod_analogs}
      - Производитель: ${product.prod_manuf || 'Не указан'}
      - Тип: ${product.prod_type}
      - Подходит для: ${product.prod_uses}, ${product.prod_model}
      - Описание: ${product.prod_note ? product.prod_note.substring(0, 200) + '...' : 'Нет описания'}
      - В наличии: ${product.qty || 0} шт.`
      })
      .join('\n\n')
  }

  async aiChat({
    message,
    threadId,
    conversationHistory = [],
    temperature = 0.7,
    maxRetries = 1,
    maxSteps = 5,
    topP = 1,
    resourceId
  }: {
    message: string
    threadId?: string
    conversationHistory?: ChatMessage[]
    temperature?: number
    maxRetries?: number
    maxSteps?: number
    topP?: number
    resourceId?: string
  }) {
    try {
      // Если threadId не передан, генерируем новый
      const currentThreadId = threadId || this.generateThreadId()
      const currentResourceId = resourceId || currentThreadId

      // Получаем историю сообщений
      let messages: ChatMessage[] = []

      if (conversationHistory && conversationHistory.length > 0) {
        messages = [...conversationHistory, { role: 'user', content: message }]
      } else if (threadId) {
        const historyResult = await this.getThreadHistory(threadId)
        if (historyResult.success && historyResult.messages && historyResult.messages.uiMessages) {
          const existingMessages = historyResult.messages.uiMessages.map((msg: any) => ({
            role: msg.role,
            content:
              typeof msg.content === 'string'
                ? msg.content
                : Array.isArray(msg.content)
                ? msg.content.find((c: any) => c.type === 'text')?.text || JSON.stringify(msg.content)
                : JSON.stringify(msg.content)
          }))
          messages = [...existingMessages, { role: 'user', content: message }]
        } else {
          messages = [{ role: 'user', content: message }]
        }
      } else {
        messages = [{ role: 'user', content: message }]
      }

      // Основной запрос к AI агенту
      const requestBody: MastraRequestBody = {
        messages: messages,
        runId: this.agentId,
        maxRetries: maxRetries,
        maxSteps: maxSteps,
        temperature: temperature,
        topP: topP,
        runtimeContext: {},
        threadId: currentThreadId,
        resourceId: currentResourceId
      }

      const response = await axios.post(`${this.mastraBaseUrl}/agents/${this.agentId}/generate`, requestBody, {
        headers: {
          'Content-Type': 'application/json',
          'Accept': '*/*'
        },
        timeout: 120000
      })
      

      const mastraResponse: MastraResponse = response.data
      const responseText = mastraResponse.text || mastraResponse.content || mastraResponse.message || 'Получен пустой ответ от AI'

      // // Парсим запрос и ищем товары асинхронно
      // const productSearchParams = parseQueryContent(responseText)
      // let products: [] | undefined = undefined

      // // Если найдены поисковые запросы, выполняем поиск товаров
      // if (productSearchParams?.queries) {
      //   try {
      //     products = await productProvider.searchFromAiChat(productSearchParams.queries)
      //     console.log(`🔍 Найдено товаров: ${products?.length || 0}`)
      //   } catch (error) {
      //     console.error('Ошибка при поиске товаров:', error)
      //   }
      // }

      // // Возвращаем основной ответ сразу, товары будут переданы отдельно
      // let finalResponse = responseText

      return {
        success: true,
        response: responseText,
        // products:mastraResponse.response?.messages[1]?.content[0].result.result.data,
        threadId: currentThreadId,
        resourceId: currentResourceId,
        agentId: this.agentId,
        products: mastraResponse.response?.messages?.find(x => x.role === 'tool')?.content?.find(x => x.toolName === 'searchTool')?.result?.result?.data
      }
    } catch (error) {
      console.error('Ошибка при обращении к Mastra AI:', error)

      // Обработка различных типов ошибок
      if (axios.isAxiosError(error)) {
        if (error.code === 'ECONNREFUSED') {
          return {
            success: false,
            response: 'Сервис AI временно недоступен. Попробуйте позже.',
            error: 'CONNECTION_REFUSED',
            threadId: threadId || this.generateThreadId()
          }
        }

        if (error.response?.status === 404) {
          return {
            success: false,
            response: 'AI агент не найден. Проверьте конфигурацию.',
            error: 'AGENT_NOT_FOUND',
            threadId: threadId || this.generateThreadId()
          }
        }

        if (error.response?.status === 429) {
          return {
            success: false,
            response: 'Превышен лимит запросов. Попробуйте позже.',
            error: 'RATE_LIMIT_EXCEEDED',
            threadId: threadId || this.generateThreadId()
          }
        }

        if (error.response?.status === 500) {
          return {
            success: false,
            response: 'Внутренняя ошибка сервера AI. Попробуйте позже.',
            error: 'INTERNAL_SERVER_ERROR',
            threadId: threadId || this.generateThreadId()
          }
        }
      }

      // Возвращаем fallback ответ
      return {
        success: false,
        response: `Извините, произошла ошибка при обработке вашего сообщения: "${message}". Попробуйте еще раз.`,
        error: 'UNKNOWN_ERROR',
        threadId: threadId || this.generateThreadId()
      }
    }
  }

  /**
   * Получает историю беседы по threadId используя Memory API
   */
  async getThreadHistory(threadId: string) {
    try {
      const response = await axios.get(`${this.mastraBaseUrl}/memory/threads/${threadId}/messages`, {
        params: {
          agentId: this.agentId,
          limit: 100 // Получаем последние 100 сообщений
        }
      })
      return {
        success: true,
        threadId: threadId,
        messages: response.data
      }
    } catch (error) {
      console.error('Ошибка при получении истории беседы:', error)
      return {
        success: false,
        error: 'Не удалось получить историю беседы',
        threadId: threadId
      }
    }
  }

  /**
   * Удаляет историю беседы по threadId используя Memory API
   */
  async deleteThread(threadId: string) {
    try {
      await axios.delete(`${this.mastraBaseUrl}/memory/threads/${threadId}`, {
        params: {
          agentId: this.agentId
        }
      })
      return {
        success: true,
        threadId: threadId,
        message: 'История беседы удалена'
      }
    } catch (error) {
      console.error('Ошибка при удалении истории беседы:', error)
      return {
        success: false,
        error: 'Не удалось удалить историю беседы',
        threadId: threadId
      }
    }
  }

  /**
   * Получает список всех тредов для указанного resourceId
   */
  async getThreadsList(resourceId: string) {
    try {
      const response = await axios.get(`${this.mastraBaseUrl}/memory/threads`, {
        params: {
          agentId: this.agentId,
          resourceid: resourceId
        }
      })
      return {
        success: true,
        threads: response.data
      }
    } catch (error) {
      console.error('Ошибка при получении списка тредов:', error)
      return {
        success: false,
        error: 'Не удалось получить список тредов'
      }
    }
  }

  // Метод для получения информации об агенте
  async getAgentInfo() {
    try {
      const response = await axios.get(`${this.mastraBaseUrl}/agents/${this.agentId}`)
      return {
        success: true,
        agent: response.data
      }
    } catch (error) {
      console.error('Ошибка при получении информации об агенте:', error)
      return {
        success: false,
        error: 'Не удалось получить информацию об агенте'
      }
    }
  }

  // Метод для проверки здоровья Mastra API
  async healthCheck() {
    try {
      const response = await axios.get(`${this.mastraBaseUrl}/health`, { timeout: 5000 })
      return {
        success: true,
        status: response.data
      }
    } catch (error) {
      return {
        success: false,
        error: 'Mastra API недоступен'
      }
    }
  }

  /**
   * Обогащает существующий ответ информацией о найденных товарах
   * Этот метод можно вызывать асинхронно после основного ответа
   */
  async enrichResponseWithProducts({
    originalResponse,
    products,
    threadId,
    resourceId,
    userMessage,
    temperature = 0.3
  }: {
    originalResponse: string
    products: any[]
    threadId: string
    resourceId: string
    userMessage: string
    temperature?: number
  }) {
    try {
      if (!products || products.length === 0) {
        return {
          success: false,
          response: originalResponse,
          error: 'Нет товаров для обогащения ответа'
        }
      }

      // Формируем краткую информацию о найденных товарах
      const productsInfo = this.formatProductsForContext(products)

      // Создаем сообщения для обогащения
      const enrichmentMessages: ChatMessage[] = [
        {
          role: 'user',
          content: userMessage
        },
        {
          role: 'assistant',
          content: originalResponse
        },
        {
          role: 'system',
          content: `По запросу пользователя найдены следующие товары:\n${productsInfo}\n\nДополни предыдущий ответ краткой и полезной информацией о найденных товарах. Не повторяй весь ответ, а только добавь релевантную информацию о товарах.`
        }
      ]

      const requestBody: MastraRequestBody = {
        messages: enrichmentMessages,
        runId: this.agentId,
        maxRetries: 1,
        maxSteps: 2,
        temperature: temperature,
        topP: 0.9,
        runtimeContext: {
          products: products,
          enrichmentMode: true
        },
        threadId: threadId,
        resourceId: resourceId
      }

      const response = await axios.post(`${this.mastraBaseUrl}/agents/${this.agentId}/generate`, requestBody, {
        headers: {
          'Content-Type': 'application/json',
          'Accept': '*/*'
        },
        timeout: 30000 // Короткий таймаут для быстрого обогащения
      })

      const mastraResponse: MastraResponse = response.data
      const enrichmentText = mastraResponse.text || mastraResponse.content || mastraResponse.message

      if (enrichmentText && enrichmentText.trim().length > 0) {
        return {
          success: true,
          response: `${originalResponse}\n\n${enrichmentText}`,
          enrichmentText: enrichmentText
        }
      } else {
        return {
          success: false,
          response: originalResponse,
          error: 'Пустой ответ при обогащении'
        }
      }
    } catch (error) {
      console.error('Ошибка при обогащении ответа товарами:', error)
      return {
        success: false,
        response: originalResponse,
        error: 'Ошибка при обогащении ответа'
      }
    }
  }

  /**
   * Оптимизированный метод чата с быстрым ответом и асинхронным обогащением
   * Возвращает ответ сразу, а поиск товаров выполняется в фоне
   */
  async aiChatOptimized({
    message,
    threadId,
    conversationHistory = [],
    temperature = 0.5,
    maxRetries = 2,
    maxSteps = 5,
    topP = 1,
    resourceId,
    enableProductSearch = true
  }: {
    message: string
    threadId?: string
    conversationHistory?: ChatMessage[]
    temperature?: number
    maxRetries?: number
    maxSteps?: number
    topP?: number
    resourceId?: string
    enableProductSearch?: boolean
  }) {
    try {
      // Если threadId не передан, генерируем новый
      const currentThreadId = threadId || this.generateThreadId()
      const currentResourceId = resourceId || currentThreadId

      // Получаем историю сообщений
      let messages: ChatMessage[] = []

      if (conversationHistory && conversationHistory.length > 0) {
        messages = [...conversationHistory, { role: 'user', content: message }]
      } else if (threadId) {
        const historyResult = await this.getThreadHistory(threadId)
        if (historyResult.success && historyResult.messages && historyResult.messages.uiMessages) {
          const existingMessages = historyResult.messages.uiMessages.map((msg: any) => ({
            role: msg.role,
            content:
              typeof msg.content === 'string'
                ? msg.content
                : Array.isArray(msg.content)
                ? msg.content.find((c: any) => c.type === 'text')?.text || JSON.stringify(msg.content)
                : JSON.stringify(msg.content)
          }))
          messages = [...existingMessages, { role: 'user', content: message }]
        } else {
          messages = [{ role: 'user', content: message }]
        }
      } else {
        messages = [{ role: 'user', content: message }]
      }

      // Основной запрос к AI агенту
      const requestBody: MastraRequestBody = {
        messages: messages,
        runId: this.agentId,
        maxRetries: maxRetries,
        maxSteps: maxSteps,
        temperature: temperature,
        topP: topP,
        runtimeContext: {},
        threadId: currentThreadId,
        resourceId: currentResourceId
      }

      const response = await axios.post(`${this.mastraBaseUrl}/agents/${this.agentId}/generate`, requestBody, {
        headers: {
          'Content-Type': 'application/json',
          'Accept': '*/*'
        },
        timeout: 120000
      })

      const mastraResponse: MastraResponse = response.data
      const responseText = mastraResponse.text || mastraResponse.content || mastraResponse.message || 'Получен пустой ответ от AI'

      // Возвращаем быстрый ответ
      const quickResult = {
        success: true,
        response: responseText,
        products: undefined,
        threadId: currentThreadId,
        resourceId: currentResourceId,
        agentId: this.agentId,
        searchInProgress: false
        products: undefined
      }

      if (enableProductSearch) {
        // Парсим запрос для поиска товаров
        const productSearchParams = parseQueryContent(responseText)

        if (productSearchParams?.queries) {
          quickResult.searchInProgress = true

          // Запускаем поиск товаров асинхронно (не ждем результата)
          quickResult.products = await this
            .performAsyncProductSearch({
              queries: productSearchParams.queries,
              originalResponse: responseText,
              threadId: currentThreadId,
              resourceId: currentResourceId,
              userMessage: message
            })
            .catch((error) => {
              console.error('Ошибка при асинхронном поиске товаров:', error)
            })
        }
      }

      return quickResult
    } catch (error) {
      console.error('Ошибка при обращении к Mastra AI:', error)

      // Обработка различных типов ошибок (аналогично основному методу)
      if (axios.isAxiosError(error)) {
        if (error.code === 'ECONNREFUSED') {
          return {
            success: false,
            response: 'Сервис AI временно недоступен. Попробуйте позже.',
            error: 'CONNECTION_REFUSED',
            threadId: threadId || this.generateThreadId()
          }
        }
        // ... другие обработки ошибок
      }

      return {
        success: false,
        response: `Извините, произошла ошибка при обработке вашего сообщения: "${message}". Попробуйте еще раз.`,
        error: 'UNKNOWN_ERROR',
        threadId: threadId || this.generateThreadId()
      }
    }
  }

  /**
   * Асинхронный поиск товаров и обогащение ответа
   * Этот метод выполняется в фоне и не блокирует основной ответ
   */
  private async performAsyncProductSearch({
    queries,
    originalResponse,
    threadId,
    resourceId,
    userMessage
  }: {
    queries: Array<{ q: string; filters?: string }>
    originalResponse: string
    threadId: string
    resourceId: string
    userMessage: string
  }) {
    try {
      console.log('🔍 Начинаем асинхронный поиск товаров...')

      // Выполняем поиск товаров
      const products = await productProvider.searchFromAiChat(queries)
      console.log(`🔍 Найдено товаров: ${products?.length || 0}`)

      if (products && products.length > 0) {
        // Обогащаем ответ информацией о товарах
        const enrichmentResult = await this.enrichResponseWithProducts({
          originalResponse,
          products,
          threadId,
          resourceId,
          userMessage
        })

        if (enrichmentResult.success) {
          console.log('✅ Ответ успешно обогащен информацией о товарах')
          // Здесь можно добавить логику для уведомления клиента об обновленном ответе
          // Например, через WebSocket или Server-Sent Events
        }
      }

      return products
    } catch (error) {
      console.error('Ошибка при асинхронном поиске товаров:', error)
    }
  }
}

export const aiProvider = new AiProvider()
