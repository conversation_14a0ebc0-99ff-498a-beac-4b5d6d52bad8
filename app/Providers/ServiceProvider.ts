import { <PERSON><PERSON> } from 'App/Plugins/Cdek'
import { PochtaRU } from 'App/Plugins/PochtaRU'
import { $prisma } from 'App/Services/Prisma'
import { cartProvider } from './CartProvider'
import Cart from 'App/Models/Cart'
import loadSettings from 'App/Helpers/loadSettings'
import Mail from '@ioc:Adonis/Addons/Mail'
import Env from '@ioc:Adonis/Core/Env'

export interface EmailSenderData {
  lastSendDate: string
  message: string
  subject: string
  recipients: Recipient[]
  scheduleTime: number // для интервалов в минутах
  scheduleType: 'interval' | 'weekly' // новое поле
  weeklySchedule?: number[] // массив дней недели (0-6, где 0=воскресенье)
  enabled: boolean
}

export interface Recipient {
  email: string
  note: string
  createdAt: string
  isActive: boolean
}

interface FailedEmail {
  recipient: Recipient
  subject: string
  message: string
  attempts: number
  lastAttempt: Date
}

class ServiceProvider {
  ordersDb = $prisma.orders
  orderSnapshotsDb = $prisma.orders_snapshots
  htmlChunksDb = $prisma.html_chunks
  pageDb = $prisma.pages

  constructor() {}

  async getSettings({ params }: { params: string[] }) {
    return await loadSettings(params)
  }

  // НОВЫЕ МЕТОДЫ ДЛЯ EMAIL РАССЫЛКИ

  async getEmailSenderData() {
    const res = await loadSettings(['emailsenderdata'])
    console.log('🚀 ~ ServiceProvider ~ getEmailSenderData ~ res:', res)
    const emailsenderdata: EmailSenderData = res.emailsenderdata

    if (!emailsenderdata) {
      throw new Error('Настройки рассылки не найдены')
    }

    // emailsenderdata.recipients = emailsenderdata.recipients.filter((recipient) => recipient.isActive)

    return emailsenderdata
  }

  /**
   * Валидация данных еженедельного расписания
   */
  private validateWeeklySchedule(weeklySchedule: number[]): boolean {
    if (!Array.isArray(weeklySchedule)) return false
    if (weeklySchedule.length === 0) return false

    return weeklySchedule.every((day) => Number.isInteger(day) && day >= 0 && day <= 6)
  }

  /**
   * Переопределенный метод обновления с валидацией
   */
  async updateEmailSenderData(payload: EmailSenderData) {
    // Валидация данных
    if (payload.scheduleType === 'weekly') {
      if (!payload.weeklySchedule || !this.validateWeeklySchedule(payload.weeklySchedule)) {
        throw new Error('Некорректное расписание дней недели')
      }
    }

    if (payload.scheduleType === 'interval') {
      if (!payload.scheduleTime || payload.scheduleTime <= 0) {
        throw new Error('Некорректный интервал отправки')
      }
    }

    const res = await $prisma.settings.updateMany({
      where: {
        s_key: 'emailsenderdata'
      },
      data: {
        s_value: JSON.stringify(payload)
      }
    })

    return res.count > 0 ? 'success' : 'error'
  }

  private async sendEmailWithRetry(recipient: Recipient, subject: string, message: string, maxRetries = 3): Promise<boolean> {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        await Mail.use('robot_news').send((mail) => {
          mail.from(Env.get('EMAIL_NEWSROBOT_FROM'), Env.get('EMAIL_FROM_NAME'))
            .to(recipient.email)
            .subject(subject)
            .html(message)
        })
        return true
      } catch (error: any) {
        const isTemporaryError = error.responseCode === 451 || 
                                error.responseCode === 421 || 
                                error.code === 'EENVELOPE'

        if (isTemporaryError && attempt < maxRetries) {
          // Экспоненциальная задержка: 2^attempt * 1000ms
          const delay = Math.pow(2, attempt) * 1000
          console.log(`Попытка ${attempt} неудачна для ${recipient.email}, повтор через ${delay}ms`)
          await new Promise((resolve) => setTimeout(resolve, delay))
          continue
        }

        console.error(`Окончательная ошибка отправки на ${recipient.email} после ${attempt} попыток:`, error)
        return false
      }
    }
    return false
  }

  async sendBulkEmail({ batchSize = 20, delayBetweenBatches = 15000 }) {
    const data = await this.getEmailSenderData()
    const { subject, message } = data

    if (!data.enabled) {
      throw new Error('Рассылка отключена')
    }

    const recipients = data.recipients.filter((recipient) => recipient.isActive)
    const batches = this.chunkArray(recipients, batchSize)

    let sentCount = 0
    let failedCount = 0

    for (const batch of batches) {
      for (const recipient of batch) {
        const success = await this.sendEmailWithRetry(recipient, subject, message)
        
        if (success) {
          sentCount++
        } else {
          failedCount++
          // Добавляем в очередь для повторной отправки
          this.failedEmailsQueue.push({
            recipient,
            subject,
            message,
            attempts: 1,
            lastAttempt: new Date()
          })
        }

        // Задержка между письмами
        await new Promise((resolve) => setTimeout(resolve, 2000))
      }

      console.log(`Батч завершен. Отправлено: ${sentCount}, Ошибок: ${failedCount}`)
      await new Promise((resolve) => setTimeout(resolve, delayBetweenBatches))
    }

    return { 
      success: true, 
      sentCount, 
      failedCount,
      queuedForRetry: this.failedEmailsQueue.length
    }
  }

  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = []
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size))
    }
    return chunks
  }

  async calculateShipping({ cartId, type = ['standard'], country = 643, destinationIndex = 101000, city, address }) {
    const pochtaRU = new PochtaRU()
    const cdek = new Cdek()

    const [cartSum, cartWeight] = await Promise.all([cartProvider.getCartSum({ cartId }), cartProvider.getCartWeight({ cartId })])

    const shippingTypes = Array.isArray(type) ? type : [type]

    const results = await Promise.all(
      shippingTypes.map(async (shipType): Promise<ShippingResult> => {
        try {
          if (shipType === 'cdek') {
            const cdekres = await cdek.calculate({
              address,
              city,
              country_code: 'RU',
              postal_code: destinationIndex,
              weight: cartWeight
            })

            return {
              type: shipType,
              price: cdekres?.total_sum || 901
            }
          }

          const price = await (await pochtaRU.calculate(country, destinationIndex, cartSum.sum))[shipType](cartWeight)
          return {
            type: shipType,
            price
          }
        } catch (error) {
          const fallbackPrice = await pochtaRU.manualCalc({
            orderprice: cartSum.sum,
            type: shipType,
            countryID: country
          })

          return {
            type: shipType,
            price: fallbackPrice,
            error: 'Использован резервный метод расчета'
          }
        }
      })
    )

    return results
  }

  async ordersList({ searchValue, limit = 50, page = 1 }: OrderListParams) {
    if (searchValue?.length > 2) {
      limit = 5
    }

    const snaps = await this.orderSnapshotsDb.findMany({
      where: {
        body: searchValue?.length
          ? {
              contains: String(searchValue)
            }
          : {}
      },
      take: limit,
      skip: (page - 1) * limit,
      orderBy: {
        ID: 'desc'
      }
    })

    const ordersData: any[] = []

    const uniqueSnaps = Array.from(new Map(snaps.sort((a, b) => b.ID - a.ID).map((snap) => [snap.orderid, snap])).values())

    uniqueSnaps.forEach((snapshot) => {
      try {
        let body = JSON.parse(snapshot.body)
        ordersData.push(body)
      } catch (error) {}
    })

    return ordersData
  }

  async getPage({ id, locale = 'ru' }) {
    return await this.pageDb.findFirst({
      where: {
        OR: [
          !isNaN(id)
            ? {
                page_id: id
              }
            : {},
          {
            page_key: id
          }
        ],
        page_locale: locale
      }
    })
  }

  async getHtmlChunk({ id }: { id: number }) {
    return await this.htmlChunksDb.findUniqueOrThrow({
      where: {
        id
      }
    })
  }

  /**
   * Вычисляет дату следующего запуска для еженедельного расписания
   * @param weeklySchedule массив дней недели (0-6, где 0=воскресенье)
   * @param timeOfDay время дня в формате "HH:MM" (по умолчанию "09:00")
   * @returns Date объект следующего запуска
   */
  private calculateNextWeeklyRun(weeklySchedule: number[], timeOfDay: string = '09:00'): Date {
    if (!weeklySchedule || weeklySchedule.length === 0) {
      throw new Error('Расписание дней недели не может быть пустым')
    }

    const now = new Date()
    const [hours, minutes] = timeOfDay.split(':').map(Number)

    // Сортируем дни недели для корректного поиска
    const sortedDays = [...weeklySchedule].sort((a, b) => a - b)

    // Проверяем, можем ли запустить сегодня
    const today = now.getDay()
    const todayScheduled = new Date(now)
    todayScheduled.setHours(hours, minutes, 0, 0)

    if (sortedDays.includes(today) && now < todayScheduled) {
      return todayScheduled
    }

    // Ищем следующий день из расписания
    let nextDay = -1

    // Сначала ищем в оставшихся днях текущей недели
    for (const day of sortedDays) {
      if (day > today) {
        nextDay = day
        break
      }
    }

    // Если не найден в текущей неделе, берем первый день из следующей недели
    if (nextDay === -1) {
      nextDay = sortedDays[0]
    }

    // Вычисляем дату следующего запуска
    const nextRun = new Date(now)
    const daysUntilNext = nextDay > today ? nextDay - today : 7 - today + nextDay

    nextRun.setDate(now.getDate() + daysUntilNext)
    nextRun.setHours(hours, minutes, 0, 0)

    return nextRun
  }

  /**
   * Проверяет, нужно ли запускать рассылку сейчас
   * @param emailData данные рассылки
   * @returns boolean
   */
  private shouldSendNow(emailData: EmailSenderData): boolean {
    if (!emailData.enabled) {
      return false
    }

    const now = new Date()
    const lastSend = emailData.lastSendDate ? new Date(emailData.lastSendDate) : null

    if (emailData.scheduleType === 'interval') {
      // Логика для интервального расписания (существующая)
      if (!lastSend) return true

      const intervalMs = emailData.scheduleTime * 60 * 1000 // минуты в миллисекунды
      return now.getTime() - lastSend.getTime() >= intervalMs
    }

    if (emailData.scheduleType === 'weekly') {
      // Логика для еженедельного расписания
      if (!emailData.weeklySchedule || emailData.weeklySchedule.length === 0) {
        return false
      }

      const today = now.getDay()
      const isScheduledDay = emailData.weeklySchedule.includes(today)

      if (!isScheduledDay) {
        return false
      }

      // Проверяем, не отправляли ли уже сегодня
      if (lastSend) {
        const lastSendDate = new Date(lastSend)
        const isToday = lastSendDate.toDateString() === now.toDateString()
        if (isToday) {
          return false
        }
      }

      return true
    }

    return false
  }

  /**
   * Получает информацию о следующем запланированном запуске
   * @param emailData данные рассылки
   * @returns объект с информацией о следующем запуске
   */
  async getNextScheduledRun(emailData?: EmailSenderData) {
    const data = emailData || (await this.getEmailSenderData())

    if (!data.enabled) {
      return {
        nextRun: null,
        message: 'Рассылка отключена'
      }
    }

    if (data.scheduleType === 'interval') {
      const lastSend = data.lastSendDate ? new Date(data.lastSendDate) : new Date()
      const nextRun = new Date(lastSend.getTime() + data.scheduleTime * 60 * 1000)

      return {
        nextRun,
        message: `Следующая отправка через ${data.scheduleTime} минут после последней`
      }
    }

    if (data.scheduleType === 'weekly') {
      if (!data.weeklySchedule || data.weeklySchedule.length === 0) {
        return {
          nextRun: null,
          message: 'Не выбраны дни недели для отправки'
        }
      }

      const nextRun = this.calculateNextWeeklyRun(data.weeklySchedule)
      const dayNames = ['Воскресенье', 'Понедельник', 'Вторник', 'Среда', 'Четверг', 'Пятница', 'Суббота']
      const selectedDays = data.weeklySchedule.map((day) => dayNames[day]).join(', ')

      return {
        nextRun,
        message: `Следующая отправка: ${nextRun.toLocaleString('ru-RU')} (дни: ${selectedDays})`
      }
    }

    return {
      nextRun: null,
      message: 'Неизвестный тип расписания'
    }
  }

  /**
   * Обновленный метод отправки с учетом нового расписания
   */
  async sendScheduledEmail({ batchSize = 100 } = {}) {
    const data = await this.getEmailSenderData()

    if (!this.shouldSendNow(data)) {
      const scheduleInfo = await this.getNextScheduledRun(data)
      throw new Error(`Рассылка не запланирована сейчас. ${scheduleInfo.message}`)
    }

    const result = await this.sendBulkEmail({ batchSize })

    // Обновляем время последней отправки
    await this.updateEmailSenderData({
      ...data,
      lastSendDate: new Date().toISOString()
    })

    return result
  }



  private failedEmailsQueue: FailedEmail[] = []

  async retryFailedEmails() {
    if (this.failedEmailsQueue.length === 0) {
      return { message: 'Нет писем для повторной отправки' }
    }

    let retriedCount = 0
    let stillFailedCount = 0

    // Фильтруем письма, которые можно повторить (не более 5 попыток)
    const emailsToRetry = this.failedEmailsQueue.filter(email => 
      email.attempts < 5 && 
      Date.now() - email.lastAttempt.getTime() > 300000 // 5 минут с последней попытки
    )

    for (const failedEmail of emailsToRetry) {
      const success = await this.sendEmailWithRetry(
        failedEmail.recipient, 
        failedEmail.subject, 
        failedEmail.message, 
        1 // только одна попытка в retry
      )

      if (success) {
        retriedCount++
        // Удаляем из очереди
        const index = this.failedEmailsQueue.indexOf(failedEmail)
        this.failedEmailsQueue.splice(index, 1)
      } else {
        stillFailedCount++
        failedEmail.attempts++
        failedEmail.lastAttempt = new Date()
      }

      // Задержка между повторными отправками
      await new Promise((resolve) => setTimeout(resolve, 3000))
    }

    return {
      retriedCount,
      stillFailedCount,
      remainingInQueue: this.failedEmailsQueue.length
    }
  }

}

export const serviceProvider = new ServiceProvider()
